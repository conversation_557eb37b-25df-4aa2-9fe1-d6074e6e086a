import React from 'react'

interface IconProps {
  className?: string
  size?: number
}

export const PropertyIcon: React.FC<IconProps> = ({ className = 'w-6 h-6', size }) => (
  <svg className={className} width={size} height={size} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
    />
  </svg>
)

export const BuildingIcon: React.FC<IconProps> = ({ className = 'w-6 h-6', size }) => (
  <svg className={className} width={size} height={size} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
    />
  </svg>
)

export const KeyIcon: React.FC<IconProps> = ({ className = 'w-6 h-6', size }) => (
  <svg className={className} width={size} height={size} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
    />
  </svg>
)

export const LocationIcon: React.FC<IconProps> = ({ className = 'w-6 h-6', size }) => (
  <svg className={className} width={size} height={size} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
    />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
)

export const TrendingUpIcon: React.FC<IconProps> = ({ className = 'w-6 h-6', size }) => (
  <svg className={className} width={size} height={size} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
  </svg>
)

export const ShieldCheckIcon: React.FC<IconProps> = ({ className = 'w-6 h-6', size }) => (
  <svg className={className} width={size} height={size} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
    />
  </svg>
)

// Decorative architectural pattern component
export const ArchitecturalPattern: React.FC<{ className?: string }> = ({ className = 'w-full h-full' }) => (
  <svg className={className} viewBox="0 0 400 400" fill="none">
    <defs>
      <pattern id="grid-pattern" width="40" height="40" patternUnits="userSpaceOnUse">
        <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1" opacity="0.25" />
      </pattern>
      <pattern id="diagonal-pattern" width="20" height="20" patternUnits="userSpaceOnUse">
        <path d="M 0 20 L 20 0" stroke="currentColor" strokeWidth="1" opacity="0.15" />
      </pattern>
      <linearGradient id="building-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stopColor="currentColor" stopOpacity="0.3" />
        <stop offset="100%" stopColor="currentColor" stopOpacity="0.1" />
      </linearGradient>
    </defs>
    <rect width="400" height="400" fill="url(#grid-pattern)" />
    <rect width="400" height="400" fill="url(#diagonal-pattern)" />

    {/* Building silhouettes with enhanced visibility */}
    <g opacity="0.25" fill="url(#building-gradient)">
      <rect x="50" y="200" width="30" height="150" rx="2" />
      <rect x="90" y="180" width="25" height="170" rx="2" />
      <rect x="125" y="220" width="35" height="130" rx="2" />
      <rect x="170" y="160" width="40" height="190" rx="2" />
      <rect x="220" y="190" width="30" height="160" rx="2" />
      <rect x="260" y="170" width="35" height="180" rx="2" />
      <rect x="305" y="210" width="25" height="140" rx="2" />
      <rect x="340" y="185" width="30" height="165" rx="2" />
    </g>

    {/* Building details - windows */}
    <g opacity="0.15" fill="currentColor">
      <rect x="55" y="210" width="4" height="4" />
      <rect x="65" y="210" width="4" height="4" />
      <rect x="55" y="225" width="4" height="4" />
      <rect x="65" y="225" width="4" height="4" />

      <rect x="95" y="190" width="3" height="3" />
      <rect x="105" y="190" width="3" height="3" />
      <rect x="95" y="205" width="3" height="3" />
      <rect x="105" y="205" width="3" height="3" />

      <rect x="175" y="170" width="5" height="5" />
      <rect x="190" y="170" width="5" height="5" />
      <rect x="175" y="190" width="5" height="5" />
      <rect x="190" y="190" width="5" height="5" />
    </g>

    {/* Geometric shapes with better visibility */}
    <g opacity="0.20" stroke="currentColor" strokeWidth="2" fill="none">
      <circle cx="100" cy="100" r="30" />
      <rect x="200" y="50" width="60" height="60" rx="5" />
      <polygon points="320,80 350,50 380,80 350,110" />
    </g>

    {/* Additional decorative elements */}
    <g opacity="0.15" fill="currentColor">
      <circle cx="320" cy="300" r="3" />
      <circle cx="80" cy="320" r="2" />
      <circle cx="300" cy="150" r="2.5" />
    </g>
  </svg>
)

// Property card background pattern
export const PropertyCardPattern: React.FC<{ className?: string }> = ({ className = 'w-full h-full' }) => (
  <svg className={className} viewBox="0 0 200 200" fill="none">
    <defs>
      <linearGradient id="property-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" stopColor="currentColor" stopOpacity="0.05" />
        <stop offset="100%" stopColor="currentColor" stopOpacity="0.02" />
      </linearGradient>
    </defs>
    <rect width="200" height="200" fill="url(#property-gradient)" />

    {/* Subtle house outline */}
    <g opacity="0.1" stroke="currentColor" strokeWidth="1" fill="none">
      <path d="M50 150 L50 100 L100 60 L150 100 L150 150 Z" />
      <rect x="70" y="120" width="15" height="30" />
      <rect x="115" y="120" width="15" height="15" />
      <path d="M60 110 L140 110" />
    </g>
  </svg>
)

// Floating elements for background
export const FloatingElements: React.FC<{ className?: string }> = ({ className = 'absolute inset-0' }) => (
  <div className={className}>
    {/* Large property icon with subtle glow */}
    <div className="absolute top-16 left-16 w-40 h-40 opacity-20 animate-float">
      <PropertyIcon className="w-full h-full text-white drop-shadow-lg" />
    </div>

    {/* Building icon with enhanced visibility */}
    <div className="absolute bottom-24 right-12 w-32 h-32 opacity-25 animate-bounce-subtle">
      <BuildingIcon className="w-full h-full text-white/80 drop-shadow-md" />
    </div>

    {/* Golden key icon - more prominent */}
    <div className="absolute top-1/2 left-1/4 w-24 h-24 opacity-40 animate-pulse-slow">
      <KeyIcon className="w-full h-full text-real-estate-gold drop-shadow-lg" />
    </div>

    {/* Location pin with better contrast */}
    <div className="absolute top-1/3 right-1/4 w-28 h-28 opacity-30 animate-float" style={{ animationDelay: '1s' }}>
      <LocationIcon className="w-full h-full text-real-estate-gold-light drop-shadow-md" />
    </div>

    {/* Additional smaller elements for depth */}
    <div className="absolute top-3/4 left-1/3 w-16 h-16 opacity-25 animate-pulse-slow" style={{ animationDelay: '2s' }}>
      <TrendingUpIcon className="w-full h-full text-real-estate-gold/70" />
    </div>

    <div className="absolute top-1/4 right-1/2 w-20 h-20 opacity-20 animate-float" style={{ animationDelay: '0.5s' }}>
      <ShieldCheckIcon className="w-full h-full text-white/60 drop-shadow-sm" />
    </div>
  </div>
)
