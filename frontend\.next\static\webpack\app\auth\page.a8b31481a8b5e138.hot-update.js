"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/page",{

/***/ "(app-pages-browser)/./src/components/auth/SignupForm.tsx":
/*!********************************************!*\
  !*** ./src/components/auth/SignupForm.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SignupForm: () => (/* binding */ SignupForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ SignupForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Icons for enhanced UI\nconst UserIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n            lineNumber: 18,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n        lineNumber: 17,\n        columnNumber: 3\n    }, undefined);\n_c = UserIcon;\nconst EmailIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n            lineNumber: 29,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n        lineNumber: 28,\n        columnNumber: 3\n    }, undefined);\n_c1 = EmailIcon;\nconst PasswordIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n            lineNumber: 40,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n        lineNumber: 39,\n        columnNumber: 3\n    }, undefined);\n_c2 = PasswordIcon;\nconst CheckIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            strokeWidth: 2,\n            d: \"M5 13l4 4L19 7\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n            lineNumber: 51,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined);\n_c3 = CheckIcon;\nconst StarIcon = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: \"w-5 h-5\",\n        fill: \"currentColor\",\n        viewBox: \"0 0 20 20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n        }, void 0, false, {\n            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n            lineNumber: 57,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n        lineNumber: 56,\n        columnNumber: 3\n    }, undefined);\n_c4 = StarIcon;\nconst SignupForm = (param)=>{\n    let { onSuccess, onSwitchToLogin } = param;\n    _s();\n    const { signup } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: '',\n        password: '',\n        name: ''\n    });\n    const [confirmPassword, setConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [passwordStrength, setPasswordStrength] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Password strength calculation\n    const calculatePasswordStrength = (password)=>{\n        let strength = 0;\n        if (password.length >= 8) strength += 1;\n        if (/[a-z]/.test(password)) strength += 1;\n        if (/[A-Z]/.test(password)) strength += 1;\n        if (/[0-9]/.test(password)) strength += 1;\n        if (/[^A-Za-z0-9]/.test(password)) strength += 1;\n        return strength;\n    };\n    const getPasswordStrengthText = (strength)=>{\n        switch(strength){\n            case 0:\n            case 1:\n                return 'Very Weak';\n            case 2:\n                return 'Weak';\n            case 3:\n                return 'Fair';\n            case 4:\n                return 'Good';\n            case 5:\n                return 'Strong';\n            default:\n                return 'Very Weak';\n        }\n    };\n    const getPasswordStrengthColor = (strength)=>{\n        switch(strength){\n            case 0:\n            case 1:\n                return 'bg-red-500';\n            case 2:\n                return 'bg-orange-500';\n            case 3:\n                return 'bg-yellow-500';\n            case 4:\n                return 'bg-blue-500';\n            case 5:\n                return 'bg-green-500';\n            default:\n                return 'bg-gray-300';\n        }\n    };\n    const validateForm = ()=>{\n        var _formData_name;\n        const newErrors = {};\n        if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n            newErrors.name = 'Full name is required';\n        }\n        if (!formData.email) {\n            newErrors.email = 'Email is required';\n        } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            newErrors.email = 'Please enter a valid email address';\n        }\n        if (!formData.password) {\n            newErrors.password = 'Password is required';\n        } else if (formData.password.length < 8) {\n            newErrors.password = 'Password must be at least 8 characters long';\n        } else if (passwordStrength < 3) {\n            newErrors.password = 'Please choose a stronger password';\n        }\n        if (!confirmPassword) {\n            newErrors.confirmPassword = 'Please confirm your password';\n        } else if (formData.password !== confirmPassword) {\n            newErrors.confirmPassword = 'Passwords do not match';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) return;\n        setLoading(true);\n        setErrors({});\n        try {\n            await signup(formData.email, formData.password, formData.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess();\n        } catch (error) {\n            setErrors({\n                general: error instanceof Error ? error.message : 'Signup failed'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleInputChange = (field)=>(e)=>{\n            setFormData((prev)=>({\n                    ...prev,\n                    [field]: e.target.value\n                }));\n            if (errors[field]) {\n                setErrors((prev)=>({\n                        ...prev,\n                        [field]: undefined\n                    }));\n            }\n        };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n        className: \"w-full max-w-md mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-center text-foreground\",\n                        children: \"Create Account\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center text-muted mt-2\",\n                        children: \"Join us to get started\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-4\",\n                        children: [\n                            errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-destructive/10 border border-destructive/20 rounded-md\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-destructive\",\n                                    children: errors.general\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Full Name (Optional)\",\n                                type: \"text\",\n                                value: formData.name,\n                                onChange: handleInputChange('name'),\n                                placeholder: \"Enter your full name\",\n                                error: errors.name\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Email Address\",\n                                type: \"email\",\n                                value: formData.email,\n                                onChange: handleInputChange('email'),\n                                placeholder: \"Enter your email\",\n                                error: errors.email,\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Password\",\n                                type: \"password\",\n                                value: formData.password,\n                                onChange: handleInputChange('password'),\n                                placeholder: \"Create a password\",\n                                error: errors.password,\n                                helperText: \"Must be at least 8 characters long\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Confirm Password\",\n                                type: \"password\",\n                                value: confirmPassword,\n                                onChange: (e)=>{\n                                    setConfirmPassword(e.target.value);\n                                    if (errors.confirmPassword) {\n                                        setErrors((prev)=>({\n                                                ...prev,\n                                                confirmPassword: undefined\n                                            }));\n                                    }\n                                },\n                                placeholder: \"Confirm your password\",\n                                error: errors.confirmPassword,\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                type: \"submit\",\n                                className: \"w-full\",\n                                loading: loading,\n                                size: \"lg\",\n                                children: \"Create Account\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted\",\n                            children: [\n                                \"Already have an account?\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onSwitchToLogin,\n                                    className: \"text-primary hover:text-primary-dark font-medium transition-colors\",\n                                    children: \"Sign in\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\components\\\\auth\\\\SignupForm.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SignupForm, \"dAQYLnczZ3j/Dy2kAL1pbdbwDFM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c5 = SignupForm;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"UserIcon\");\n$RefreshReg$(_c1, \"EmailIcon\");\n$RefreshReg$(_c2, \"PasswordIcon\");\n$RefreshReg$(_c3, \"CheckIcon\");\n$RefreshReg$(_c4, \"StarIcon\");\n$RefreshReg$(_c5, \"SignupForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/SignupForm.tsx\n"));

/***/ })

});