@import 'tailwindcss';

:root {
  --background: #ffffff;
  --foreground: #1e293b;
  --primary: #1e293b;
  --primary-dark: #0f172a;
  --primary-light: #e2e8f0;
  --secondary: #64748b;
  --muted: #64748b;
  --border: #e2e8f0;
  --input: #f8fafc;
  --card: #ffffff;
  --destructive: #ef4444;
  --nav-bg: #ffffff;
  --section-dark: #0f172a;
  --section-light: #f8fafc;
  --hero-bg: #f0fdf4;
  --blue-dark: #1e293b;
  --blue-darker: #0f172a;

  /* Enhanced Auth Design Tokens */
  --auth-bg-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  --auth-card-bg: rgba(255, 255, 255, 0.95);
  --auth-card-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --auth-input-bg: #ffffff;
  --auth-input-border: #e2e8f0;
  --auth-input-focus: #1e293b;
  --auth-button-gradient: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  --auth-button-hover: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --auth-accent: #3b82f6;
  --auth-accent-light: #dbeafe;
  --auth-text-primary: #1e293b;
  --auth-text-secondary: #64748b;
  --auth-text-muted: #94a3b8;

  /* Real Estate Theme Colors */
  --real-estate-gold: #d4af37;
  --real-estate-gold-light: #f7e98e;
  --real-estate-silver: #c0c0c0;
  --real-estate-bronze: #cd7f32;

  /* Enhanced Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-primary-light: var(--primary-light);
  --color-secondary: var(--secondary);
  --color-muted: var(--muted);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-card: var(--card);
  --color-destructive: var(--destructive);
  --color-nav-bg: var(--nav-bg);
  --color-section-dark: var(--section-dark);
  --color-section-light: var(--section-light);
  --color-hero-bg: var(--hero-bg);
  --color-blue-dark: var(--blue-dark);
  --color-blue-darker: var(--blue-darker);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #1e293b;
    --secondary: #1e293b;
    --muted: #64748b;
    --border: #e2e8f0;
    --input: #f8fafc;
    --card: #ffffff;
    --nav-bg: #ffffff;
    --section-dark: #0f172a;
    --section-light: #f8fafc;
    --hero-bg: #f0fdf4;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* Custom animations and transitions */
.animate-fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-subtle {
  animation: bounceSubtle 2s infinite;
}

/* Enhanced input animations */
.input-float-label {
  position: relative;
}

.input-float-label input:focus + label,
.input-float-label input:not(:placeholder-shown) + label {
  transform: translateY(-1.5rem) scale(0.85);
  color: var(--auth-input-focus);
}

.input-float-label label {
  position: absolute;
  left: 0.75rem;
  top: 0.75rem;
  transition: all 0.2s ease-in-out;
  pointer-events: none;
  color: var(--auth-text-muted);
}

/* Gradient text effect */
.gradient-text {
  background: var(--auth-button-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounceSubtle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Responsive design utilities */
@media (max-width: 768px) {
  .auth-split-layout {
    flex-direction: column;
  }

  .auth-image-section {
    height: 200px;
  }
}

/* Enhanced hover effects */
.hover-lift {
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(50, 205, 50, 0.3);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Real Estate Themed Animations */
@keyframes property-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(212, 175, 55, 0.5);
  }
}

@keyframes building-rise {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-property-glow {
  animation: property-glow 2s ease-in-out infinite;
}

.animate-building-rise {
  animation: building-rise 0.8s ease-out forwards;
}

/* Real Estate themed utility classes */
.real-estate-gradient {
  background: linear-gradient(135deg, var(--real-estate-gold) 0%, var(--real-estate-bronze) 100%);
}

.real-estate-text-gradient {
  background: linear-gradient(135deg, var(--real-estate-gold) 0%, var(--real-estate-gold-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced visual effects for real estate elements */
.real-estate-glow {
  filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.4));
}

.real-estate-icon-glow {
  filter: drop-shadow(0 0 8px rgba(212, 175, 55, 0.3)) drop-shadow(0 0 16px rgba(212, 175, 55, 0.1));
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(2) {
  animation-delay: -2s;
}

.floating-element:nth-child(3) {
  animation-delay: -4s;
}

/* Enhanced background patterns */
.auth-background-overlay {
  background:
    radial-gradient(circle at 20% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(205, 127, 50, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(212, 175, 55, 0.05) 0%, transparent 50%);
}
