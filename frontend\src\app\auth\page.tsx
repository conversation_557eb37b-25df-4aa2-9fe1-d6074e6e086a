'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { LoginForm } from '@/components/auth/LoginForm'
import { SignupForm } from '@/components/auth/SignupForm'
import {
  FloatingElements,
  ArchitecturalPattern,
  PropertyIcon,
  KeyIcon,
  BuildingIcon
} from '@/components/ui/RealEstateIcons'

type AuthMode = 'login' | 'signup'

export default function AuthPage() {
  const [mode, setMode] = useState<AuthMode>('login')
  const router = useRouter()

  const handleAuthSuccess = () => {
    // Redirect to dashboard or home page after successful authentication
    router.push('/')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-dark via-blue-darker to-section-dark flex">
      {/* Left Side - Branding and Visual */}
      <div className="hidden lg:flex lg:w-1/2 relative overflow-hidden">
        {/* Enhanced Background with Multiple Layers */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-dark via-blue-darker to-section-dark"></div>
        <div className="absolute inset-0 bg-gradient-to-tr from-real-estate-gold/10 via-transparent to-real-estate-bronze/5"></div>
        <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-real-estate-gold/5 to-blue-darker/80"></div>
        <div className="absolute inset-0 bg-gradient-to-tl from-real-estate-lime/8 via-transparent to-real-estate-lime-light/4"></div>
        <div className="absolute inset-0 auth-background-overlay"></div>
        <div className="absolute inset-0 auth-background-lime-overlay"></div>

        {/* Real Estate Themed Floating Elements */}
        <FloatingElements />

        {/* Content */}
        <div className="relative z-10 flex flex-col justify-center items-start p-16 text-white">
          <div className="animate-slide-in-left">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Welcome to <br />
              <span className="gradient-text bg-gradient-to-r from-real-estate-gold via-real-estate-lime to-real-estate-gold-light bg-clip-text text-transparent real-estate-mixed-glow animate-lime-glow-pulse">
                MyRealHub
              </span>
            </h1>
            <p className="text-xl mb-8 text-white/90 leading-relaxed">
              Your premium real estate management platform. Discover, manage, and grow your property portfolio with
              confidence.
            </p>

            {/* Feature highlights */}
            <div className="space-y-5">
              <div className="flex items-center space-x-4 group">
                <div className="w-4 h-4 bg-real-estate-lime rounded-full shadow-lg animate-pulse-slow group-hover:scale-125 transition-transform real-estate-lime-glow"></div>
                <span className="text-white/90 font-medium group-hover:text-real-estate-lime-light transition-colors">
                  Advanced Property Analytics
                </span>
              </div>
              <div className="flex items-center space-x-4 group">
                <div
                  className="w-4 h-4 bg-real-estate-gold rounded-full shadow-lg animate-pulse-slow group-hover:scale-125 transition-transform real-estate-glow"
                  style={{ animationDelay: '0.5s' }}
                ></div>
                <span className="text-white/90 font-medium group-hover:text-real-estate-gold-light transition-colors">
                  Multi-tenant Management
                </span>
              </div>
              <div className="flex items-center space-x-4 group">
                <div
                  className="w-4 h-4 bg-real-estate-lime-light rounded-full shadow-lg animate-pulse-slow group-hover:scale-125 transition-transform real-estate-lime-glow"
                  style={{ animationDelay: '1s' }}
                ></div>
                <span className="text-white/90 font-medium group-hover:text-real-estate-lime transition-colors">
                  Real-time Market Insights
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Architectural Pattern Overlay */}
        <div className="absolute bottom-0 right-0 w-80 h-80 text-real-estate-lime/70 real-estate-lime-icon-glow">
          <ArchitecturalPattern />
        </div>
      </div>

      {/* Right Side - Authentication Forms */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 lg:p-16 relative">
        {/* Enhanced background pattern for right side */}
        <div className="absolute inset-0 opacity-15">
          <div className="absolute top-10 right-10 w-32 h-32 text-real-estate-lime real-estate-lime-glow">
            <PropertyIcon className="w-full h-full" />
          </div>
          <div className="absolute bottom-20 left-10 w-24 h-24 text-real-estate-gold real-estate-glow">
            <KeyIcon className="w-full h-full" />
          </div>
          <div className="absolute top-1/2 left-5 w-20 h-20 text-real-estate-lime-light real-estate-lime-glow">
            <BuildingIcon className="w-full h-full" />
          </div>
        </div>

        <div className="w-full max-w-md animate-slide-in-right relative z-10">
          {/* Mobile Logo */}
          <div className="lg:hidden text-center mb-8">
            <h1 className="text-4xl font-bold text-white mb-2">
              <span className="gradient-text bg-gradient-to-r from-real-estate-gold via-real-estate-lime to-real-estate-gold-light bg-clip-text text-transparent real-estate-mixed-glow">
                MyRealHub
              </span>
            </h1>
            <p className="text-white/80 font-medium">Premium Real Estate Management</p>
          </div>

          {/* Authentication Forms */}
          <div className="space-y-6">
            {mode === 'login' ? (
              <LoginForm onSuccess={handleAuthSuccess} onSwitchToSignup={() => setMode('signup')} />
            ) : (
              <SignupForm onSuccess={handleAuthSuccess} onSwitchToLogin={() => setMode('login')} />
            )}
          </div>

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-xs text-white/60">
              By continuing, you agree to our{' '}
              <a href="#" className="text-real-estate-gold hover:text-real-estate-gold-light transition-colors">
                Terms of Service
              </a>{' '}
              and{' '}
              <a href="#" className="text-real-estate-gold hover:text-real-estate-gold-light transition-colors">
                Privacy Policy
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
