"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = react__WEBPACK_IMPORTED_MODULE_5___default().useState(false);\n    react__WEBPACK_IMPORTED_MODULE_5___default().useEffect({\n        \"Home.useEffect\": ()=>{\n            if (isAuthenticated) {\n                router.push('/dashboard');\n            }\n        }\n    }[\"Home.useEffect\"], [\n        isAuthenticated,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background text-foreground\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: \"/images/logo.png\",\n                                            alt: \"MyRealHub Logo\",\n                                            width: 259,\n                                            height: 64,\n                                            className: \"h-16 w-auto\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/\",\n                                            className: \"text-foreground hover:text-primary transition-colors font-medium\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"#about-us\",\n                                            className: \"text-foreground hover:text-primary transition-colors font-medium\",\n                                            children: \"About Us\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"#properties\",\n                                            className: \"text-foreground hover:text-primary transition-colors font-medium\",\n                                            children: \"Properties\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"#explore-places\",\n                                            className: \"text-foreground hover:text-primary transition-colors font-medium\",\n                                            children: \"Explore Places\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"#developers\",\n                                            className: \"text-foreground hover:text-primary transition-colors font-medium\",\n                                            children: \"Developers\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth\",\n                                            className: \"text-foreground hover:text-primary transition-colors font-medium\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth\",\n                                            className: \"inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 bg-primary text-white hover:bg-primary-dark focus:ring-primary px-4 py-2 text-sm rounded-md\",\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setIsMobileMenuOpen(!isMobileMenuOpen),\n                                        className: \"text-foreground hover:text-primary focus:outline-none focus:text-primary\",\n                                        \"aria-label\": \"Toggle menu\",\n                                        \"aria-expanded\": isMobileMenuOpen,\n                                        children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-6 w-6\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-6 w-6\",\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 6h16M4 12h16M4 18h16\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 88,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden bg-white border-t border-border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"block px-3 py-2 text-foreground hover:text-primary hover:bg-input rounded-md transition-colors\",\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"#about-us\",\n                                    className: \"block px-3 py-2 text-foreground hover:text-primary hover:bg-input rounded-md transition-colors\",\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: \"About Us\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"#properties\",\n                                    className: \"block px-3 py-2 text-foreground hover:text-primary hover:bg-input rounded-md transition-colors\",\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: \"Properties\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"#explore-places\",\n                                    className: \"block px-3 py-2 text-foreground hover:text-primary hover:bg-input rounded-md transition-colors\",\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: \"Explore Places\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"#developers\",\n                                    className: \"block px-3 py-2 text-foreground hover:text-primary hover:bg-input rounded-md transition-colors\",\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: \"Developers\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-t border-border pt-2 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth\",\n                                            className: \"block px-3 py-2 text-foreground hover:text-primary hover:bg-input rounded-md transition-colors\",\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/auth\",\n                                            className: \"block px-3 py-2 mt-1 bg-primary text-white hover:bg-primary-dark rounded-md transition-colors text-center\",\n                                            onClick: ()=>setIsMobileMenuOpen(false),\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-primary-light to-green-50 py-20 lg:py-32\",\n                role: \"banner\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground mb-6\",\n                                        children: [\n                                            \"Find Your\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-primary block\",\n                                                children: \"Dream Home\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg sm:text-xl text-muted mb-8 max-w-lg\",\n                                        children: \"Discover the perfect property with our comprehensive real estate platform. Buy, sell, or rent with confidence using our expert tools and services.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        className: \"bg-card p-6 rounded-lg shadow-lg border border-border animate-scale-in hover-glow\",\n                                        role: \"search\",\n                                        \"aria-label\": \"Property search\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"location\",\n                                                            className: \"block text-sm font-medium text-foreground mb-2\",\n                                                            children: \"Location\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"location\",\n                                                            name: \"location\",\n                                                            placeholder: \"Enter city or zip code\",\n                                                            className: \"w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors\",\n                                                            \"aria-describedby\": \"location-help\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            id: \"location-help\",\n                                                            className: \"sr-only\",\n                                                            children: \"Enter the city or zip code where you want to search for properties\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"property-type\",\n                                                            className: \"block text-sm font-medium text-foreground mb-2\",\n                                                            children: \"Property Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"property-type\",\n                                                            name: \"propertyType\",\n                                                            className: \"w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors\",\n                                                            \"aria-describedby\": \"property-type-help\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"All Types\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"house\",\n                                                                    children: \"House\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"apartment\",\n                                                                    children: \"Apartment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"condo\",\n                                                                    children: \"Condo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"townhouse\",\n                                                                    children: \"Townhouse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            id: \"property-type-help\",\n                                                            className: \"sr-only\",\n                                                            children: \"Select the type of property you are looking for\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"price-range\",\n                                                            className: \"block text-sm font-medium text-foreground mb-2\",\n                                                            children: \"Price Range\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            id: \"price-range\",\n                                                            name: \"priceRange\",\n                                                            className: \"w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors\",\n                                                            \"aria-describedby\": \"price-range-help\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"Any Price\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"0-200000\",\n                                                                    children: \"Under $200K\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"200000-400000\",\n                                                                    children: \"$200K - $400K\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"400000-600000\",\n                                                                    children: \"$400K - $600K\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"600000+\",\n                                                                    children: \"$600K+\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            id: \"price-range-help\",\n                                                            className: \"sr-only\",\n                                                            children: \"Select your preferred price range for properties\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-end\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                        type: \"submit\",\n                                                        className: \"w-full\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sr-only\",\n                                                                children: \"Search for\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Search Properties\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-[4/3] bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-24 h-24 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-12 h-12 text-primary\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    stroke: \"currentColor\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted\",\n                                                children: \"Beautiful Property Image\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 lg:py-24 bg-card\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl lg:text-4xl font-bold text-foreground mb-4\",\n                                    children: \"Featured Properties\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-muted max-w-2xl mx-auto\",\n                                    children: \"Discover our handpicked selection of premium properties available for sale and rent\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-card rounded-lg shadow-lg border border-border overflow-hidden hover-lift animate-fade-in group cursor-pointer\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-[4/3] bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-primary\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted\",\n                                                        children: \"Property Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-foreground\",\n                                                            children: \"Modern Family Home\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl font-bold text-primary\",\n                                                            children: \"$485,000\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-4\",\n                                                    children: \"123 Oak Street, Downtown District\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm text-muted mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDECF️ 4 Beds\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDEBF 3 Baths\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDCD0 2,400 sq ft\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    children: \"View Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-card rounded-lg shadow-lg border border-border overflow-hidden hover:shadow-xl transition-shadow duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-[4/3] bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-primary\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted\",\n                                                        children: \"Property Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-foreground\",\n                                                            children: \"Luxury Apartment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl font-bold text-primary\",\n                                                            children: \"$2,800/mo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-4\",\n                                                    children: \"456 Pine Avenue, Uptown Area\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm text-muted mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDECF️ 2 Beds\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDEBF 2 Baths\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDCD0 1,200 sq ft\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    children: \"View Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-card rounded-lg shadow-lg border border-border overflow-hidden hover:shadow-xl transition-shadow duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-[4/3] bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-8 h-8 text-primary\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10v11M20 10v11\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted\",\n                                                        children: \"Property Image\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-semibold text-foreground\",\n                                                            children: \"Cozy Townhouse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl font-bold text-primary\",\n                                                            children: \"$325,000\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-4\",\n                                                    children: \"789 Maple Lane, Suburban Heights\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-sm text-muted mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDECF️ 3 Beds\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDEBF 2 Baths\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"\\uD83D\\uDCD0 1,800 sq ft\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    children: \"View Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                size: \"lg\",\n                                children: \"View All Properties\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 266,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 lg:py-24 bg-background\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl lg:text-4xl font-bold text-foreground mb-4\",\n                                    children: \"Why Choose MyRealHub?\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-muted max-w-2xl mx-auto\",\n                                    children: \"Experience the future of real estate with our comprehensive platform designed for modern property transactions\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-3\",\n                                            children: \"Find Your Dream Home\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted\",\n                                            children: \"Advanced search filters and AI-powered recommendations help you discover the perfect property that matches your lifestyle and budget.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-3\",\n                                            children: \"Expert Agents\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted\",\n                                            children: \"Connect with certified real estate professionals who provide personalized guidance throughout your buying or selling journey.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-3\",\n                                            children: \"Market Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted\",\n                                            children: \"Access real-time market data, price trends, and neighborhood insights to make informed decisions with confidence.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-3\",\n                                            children: \"Secure Transactions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted\",\n                                            children: \"Bank-level security and encrypted communications ensure your personal information and transactions are always protected.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-3\",\n                                            children: \"Premium Service\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted\",\n                                            children: \"Enjoy white-glove service with dedicated support, virtual tours, and personalized property recommendations.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-8 h-8 text-white\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-foreground mb-3\",\n                                            children: \"Smart Financing\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted\",\n                                            children: \"Access competitive mortgage rates, pre-approval tools, and financing options tailored to your financial situation.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-secondary text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: \"/images/logo.png\",\n                                                alt: \"MyRealHub Logo\",\n                                                width: 129,\n                                                height: 32,\n                                                className: \"h-8 w-auto brightness-0 invert\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 mb-4\",\n                                            children: \"Your trusted partner in real estate. We help you find, buy, sell, and rent properties with confidence and ease.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                    \"aria-label\": \"Facebook\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                    \"aria-label\": \"Twitter\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                    \"aria-label\": \"LinkedIn\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                    \"aria-label\": \"Instagram\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-6 h-6\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 555,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#buy\",\n                                                        className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                        children: \"Buy Properties\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#sell\",\n                                                        className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                        children: \"Sell Your Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#rent\",\n                                                        className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                        children: \"Rent Properties\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#agents\",\n                                                        className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                        children: \"Find Agents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#market\",\n                                                        className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                        children: \"Market Insights\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#valuation\",\n                                                        className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                        children: \"Property Valuation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#financing\",\n                                                        className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                        children: \"Mortgage Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#legal\",\n                                                        className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                        children: \"Legal Support\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 609,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#inspection\",\n                                                        className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                        children: \"Home Inspection\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#insurance\",\n                                                        className: \"text-gray-300 hover:text-primary transition-colors\",\n                                                        children: \"Property Insurance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-4\",\n                                            children: \"Contact Us\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 text-primary mr-3\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: [\n                                                                \"123 Real Estate Ave\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \"Suite 100, City, ST 12345\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 631,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 text-primary mr-3\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 653,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"(555) 123-4567\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 661,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-5 h-5 text-primary mr-3\",\n                                                            fill: \"none\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            stroke: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-600 mt-8 pt-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300\",\n                                children: [\n                                    \"\\xa9 2024 MyRealHub. All rights reserved. |\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#privacy\",\n                                        className: \"hover:text-primary transition-colors ml-1\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 15\n                                    }, this),\n                                    ' ',\n                                    \"|\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#terms\",\n                                        className: \"hover:text-primary transition-colors ml-1\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 679,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 522,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 521,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Code\\\\Portfolio\\\\NewMRH\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"c/PJTG9WFaGhG8RyF5VZMu7n2is=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRThCO0FBQ0Y7QUFDb0I7QUFDTDtBQUNsQjtBQUNzQjtBQUVoQyxTQUFTTTs7SUFDdEIsTUFBTSxFQUFFQyxlQUFlLEVBQUUsR0FBR0wsOERBQU9BO0lBQ25DLE1BQU1NLFNBQVNMLDBEQUFTQTtJQUN4QixNQUFNLENBQUNNLGtCQUFrQkMsb0JBQW9CLEdBQUdOLHFEQUFjLENBQUM7SUFFL0RBLHNEQUFlOzBCQUFDO1lBQ2QsSUFBSUcsaUJBQWlCO2dCQUNuQkMsT0FBT0ssSUFBSSxDQUFDO1lBQ2Q7UUFDRjt5QkFBRztRQUFDTjtRQUFpQkM7S0FBTztJQUU1QixxQkFDRSw4REFBQ007UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNkLGtEQUFJQTt3Q0FBQ2dCLE1BQUs7d0NBQUlGLFdBQVU7a0RBQ3ZCLDRFQUFDZixrREFBS0E7NENBQ0prQixLQUFJOzRDQUNKQyxLQUFJOzRDQUNKQyxPQUFPOzRDQUNQQyxRQUFROzRDQUNSTixXQUFVOzRDQUNWTyxRQUFROzs7Ozs7Ozs7Ozs7Ozs7OzhDQU1kLDhEQUFDUjtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNkLGtEQUFJQTs0Q0FBQ2dCLE1BQUs7NENBQUlGLFdBQVU7c0RBQW1FOzs7Ozs7c0RBRzVGLDhEQUFDZCxrREFBSUE7NENBQUNnQixNQUFLOzRDQUFZRixXQUFVO3NEQUFtRTs7Ozs7O3NEQUdwRyw4REFBQ2Qsa0RBQUlBOzRDQUFDZ0IsTUFBSzs0Q0FBY0YsV0FBVTtzREFBbUU7Ozs7OztzREFHdEcsOERBQUNkLGtEQUFJQTs0Q0FBQ2dCLE1BQUs7NENBQWtCRixXQUFVO3NEQUFtRTs7Ozs7O3NEQUcxRyw4REFBQ2Qsa0RBQUlBOzRDQUFDZ0IsTUFBSzs0Q0FBY0YsV0FBVTtzREFBbUU7Ozs7Ozs7Ozs7Ozs4Q0FNeEcsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2Qsa0RBQUlBOzRDQUFDZ0IsTUFBSzs0Q0FBUUYsV0FBVTtzREFBbUU7Ozs7OztzREFHaEcsOERBQUNkLGtEQUFJQTs0Q0FDSGdCLE1BQUs7NENBQ0xGLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs4Q0FNSCw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNRO3dDQUNDQyxNQUFLO3dDQUNMQyxTQUFTLElBQU1mLG9CQUFvQixDQUFDRDt3Q0FDcENNLFdBQVU7d0NBQ1ZXLGNBQVc7d0NBQ1hDLGlCQUFlbEI7a0RBRWRBLGlDQUNDLDhEQUFDbUI7NENBQUliLFdBQVU7NENBQVVjLE1BQUs7NENBQU9DLFNBQVE7NENBQVlDLFFBQU87c0RBQzlELDRFQUFDQztnREFBS0MsZUFBYztnREFBUUMsZ0JBQWU7Z0RBQVFDLGFBQWE7Z0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7aUVBR3ZFLDhEQUFDUjs0Q0FBSWIsV0FBVTs0Q0FBVWMsTUFBSzs0Q0FBT0MsU0FBUTs0Q0FBWUMsUUFBTztzREFDOUQsNEVBQUNDO2dEQUFLQyxlQUFjO2dEQUFRQyxnQkFBZTtnREFBUUMsYUFBYTtnREFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBU2hGM0Isa0NBQ0MsOERBQUNLO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNkLGtEQUFJQTtvQ0FDSGdCLE1BQUs7b0NBQ0xGLFdBQVU7b0NBQ1ZVLFNBQVMsSUFBTWYsb0JBQW9COzhDQUNwQzs7Ozs7OzhDQUdELDhEQUFDVCxrREFBSUE7b0NBQ0hnQixNQUFLO29DQUNMRixXQUFVO29DQUNWVSxTQUFTLElBQU1mLG9CQUFvQjs4Q0FDcEM7Ozs7Ozs4Q0FHRCw4REFBQ1Qsa0RBQUlBO29DQUNIZ0IsTUFBSztvQ0FDTEYsV0FBVTtvQ0FDVlUsU0FBUyxJQUFNZixvQkFBb0I7OENBQ3BDOzs7Ozs7OENBR0QsOERBQUNULGtEQUFJQTtvQ0FDSGdCLE1BQUs7b0NBQ0xGLFdBQVU7b0NBQ1ZVLFNBQVMsSUFBTWYsb0JBQW9COzhDQUNwQzs7Ozs7OzhDQUdELDhEQUFDVCxrREFBSUE7b0NBQ0hnQixNQUFLO29DQUNMRixXQUFVO29DQUNWVSxTQUFTLElBQU1mLG9CQUFvQjs4Q0FDcEM7Ozs7Ozs4Q0FHRCw4REFBQ0k7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDZCxrREFBSUE7NENBQ0hnQixNQUFLOzRDQUNMRixXQUFVOzRDQUNWVSxTQUFTLElBQU1mLG9CQUFvQjtzREFDcEM7Ozs7OztzREFHRCw4REFBQ1Qsa0RBQUlBOzRDQUNIZ0IsTUFBSzs0Q0FDTEYsV0FBVTs0Q0FDVlUsU0FBUyxJQUFNZixvQkFBb0I7c0RBQ3BDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFVWCw4REFBQzJCO2dCQUFRdEIsV0FBVTtnQkFBMkV1QixNQUFLOzBCQUNqRyw0RUFBQ3hCO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3dCO3dDQUFHeEIsV0FBVTs7NENBQWtFOzBEQUU5RSw4REFBQ3lCO2dEQUFLekIsV0FBVTswREFBcUI7Ozs7Ozs7Ozs7OztrREFFdkMsOERBQUMwQjt3Q0FBRTFCLFdBQVU7a0RBQThDOzs7Ozs7a0RBTTNELDhEQUFDMkI7d0NBQ0MzQixXQUFVO3dDQUNWdUIsTUFBSzt3Q0FDTFosY0FBVztrREFFWCw0RUFBQ1o7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDs7c0VBQ0MsOERBQUM2Qjs0REFBTUMsU0FBUTs0REFBVzdCLFdBQVU7c0VBQWlEOzs7Ozs7c0VBR3JGLDhEQUFDOEI7NERBQ0NyQixNQUFLOzREQUNMc0IsSUFBRzs0REFDSEMsTUFBSzs0REFDTEMsYUFBWTs0REFDWmpDLFdBQVU7NERBQ1ZrQyxvQkFBaUI7Ozs7OztzRUFFbkIsOERBQUNUOzREQUFLTSxJQUFHOzREQUFnQi9CLFdBQVU7c0VBQVU7Ozs7Ozs7Ozs7Ozs4REFJL0MsOERBQUNEOztzRUFDQyw4REFBQzZCOzREQUFNQyxTQUFROzREQUFnQjdCLFdBQVU7c0VBQWlEOzs7Ozs7c0VBRzFGLDhEQUFDbUM7NERBQ0NKLElBQUc7NERBQ0hDLE1BQUs7NERBQ0xoQyxXQUFVOzREQUNWa0Msb0JBQWlCOzs4RUFFakIsOERBQUNFO29FQUFPQyxPQUFNOzhFQUFHOzs7Ozs7OEVBQ2pCLDhEQUFDRDtvRUFBT0MsT0FBTTs4RUFBUTs7Ozs7OzhFQUN0Qiw4REFBQ0Q7b0VBQU9DLE9BQU07OEVBQVk7Ozs7Ozs4RUFDMUIsOERBQUNEO29FQUFPQyxPQUFNOzhFQUFROzs7Ozs7OEVBQ3RCLDhEQUFDRDtvRUFBT0MsT0FBTTs4RUFBWTs7Ozs7Ozs7Ozs7O3NFQUU1Qiw4REFBQ1o7NERBQUtNLElBQUc7NERBQXFCL0IsV0FBVTtzRUFBVTs7Ozs7Ozs7Ozs7OzhEQUlwRCw4REFBQ0Q7O3NFQUNDLDhEQUFDNkI7NERBQU1DLFNBQVE7NERBQWM3QixXQUFVO3NFQUFpRDs7Ozs7O3NFQUd4Riw4REFBQ21DOzREQUNDSixJQUFHOzREQUNIQyxNQUFLOzREQUNMaEMsV0FBVTs0REFDVmtDLG9CQUFpQjs7OEVBRWpCLDhEQUFDRTtvRUFBT0MsT0FBTTs4RUFBRzs7Ozs7OzhFQUNqQiw4REFBQ0Q7b0VBQU9DLE9BQU07OEVBQVc7Ozs7Ozs4RUFDekIsOERBQUNEO29FQUFPQyxPQUFNOzhFQUFnQjs7Ozs7OzhFQUM5Qiw4REFBQ0Q7b0VBQU9DLE9BQU07OEVBQWdCOzs7Ozs7OEVBQzlCLDhEQUFDRDtvRUFBT0MsT0FBTTs4RUFBVTs7Ozs7Ozs7Ozs7O3NFQUUxQiw4REFBQ1o7NERBQUtNLElBQUc7NERBQW1CL0IsV0FBVTtzRUFBVTs7Ozs7Ozs7Ozs7OzhEQUlsRCw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNWLHlEQUFNQTt3REFBQ21CLE1BQUs7d0RBQVNULFdBQVU7OzBFQUM5Qiw4REFBQ3lCO2dFQUFLekIsV0FBVTswRUFBVTs7Ozs7OzREQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBU3JELDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNhO29EQUFJYixXQUFVO29EQUF5QmMsTUFBSztvREFBT0MsU0FBUTtvREFBWUMsUUFBTzs4REFDN0UsNEVBQUNDO3dEQUNDQyxlQUFjO3dEQUNkQyxnQkFBZTt3REFDZkMsYUFBYTt3REFDYkMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OzswREFJUiw4REFBQ0s7Z0RBQUUxQixXQUFVOzBEQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTdEMsOERBQUNzQjtnQkFBUXRCLFdBQVU7MEJBQ2pCLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3NDO29DQUFHdEMsV0FBVTs4Q0FBc0Q7Ozs7Ozs4Q0FDcEUsOERBQUMwQjtvQ0FBRTFCLFdBQVU7OENBQXVDOzs7Ozs7Ozs7Ozs7c0NBS3RELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7a0VBQ2IsNEVBQUNhOzREQUFJYixXQUFVOzREQUF1QmMsTUFBSzs0REFBT0MsU0FBUTs0REFBWUMsUUFBTztzRUFDM0UsNEVBQUNDO2dFQUNDQyxlQUFjO2dFQUNkQyxnQkFBZTtnRUFDZkMsYUFBYTtnRUFDYkMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztrRUFJUiw4REFBQ0s7d0RBQUUxQixXQUFVO2tFQUFxQjs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBR3RDLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3VDOzREQUFHdkMsV0FBVTtzRUFBd0M7Ozs7OztzRUFDdEQsOERBQUN5Qjs0REFBS3pCLFdBQVU7c0VBQWtDOzs7Ozs7Ozs7Ozs7OERBRXBELDhEQUFDMEI7b0RBQUUxQixXQUFVOzhEQUFrQjs7Ozs7OzhEQUMvQiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDeUI7c0VBQUs7Ozs7OztzRUFDTiw4REFBQ0E7c0VBQUs7Ozs7OztzRUFDTiw4REFBQ0E7c0VBQUs7Ozs7Ozs7Ozs7Ozs4REFFUiw4REFBQ25DLHlEQUFNQTtvREFBQ2tELFNBQVE7b0RBQVV4QyxXQUFVOzhEQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBT2pELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDYTs0REFBSWIsV0FBVTs0REFBdUJjLE1BQUs7NERBQU9DLFNBQVE7NERBQVlDLFFBQU87c0VBQzNFLDRFQUFDQztnRUFDQ0MsZUFBYztnRUFDZEMsZ0JBQWU7Z0VBQ2ZDLGFBQWE7Z0VBQ2JDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBSVIsOERBQUNLO3dEQUFFMUIsV0FBVTtrRUFBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUd0Qyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN1Qzs0REFBR3ZDLFdBQVU7c0VBQXdDOzs7Ozs7c0VBQ3RELDhEQUFDeUI7NERBQUt6QixXQUFVO3NFQUFrQzs7Ozs7Ozs7Ozs7OzhEQUVwRCw4REFBQzBCO29EQUFFMUIsV0FBVTs4REFBa0I7Ozs7Ozs4REFDL0IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ3lCO3NFQUFLOzs7Ozs7c0VBQ04sOERBQUNBO3NFQUFLOzs7Ozs7c0VBQ04sOERBQUNBO3NFQUFLOzs7Ozs7Ozs7Ozs7OERBRVIsOERBQUNuQyx5REFBTUE7b0RBQUNrRCxTQUFRO29EQUFVeEMsV0FBVTs4REFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQU9qRCw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDYiw0RUFBQ2E7NERBQUliLFdBQVU7NERBQXVCYyxNQUFLOzREQUFPQyxTQUFROzREQUFZQyxRQUFPO3NFQUMzRSw0RUFBQ0M7Z0VBQ0NDLGVBQWM7Z0VBQ2RDLGdCQUFlO2dFQUNmQyxhQUFhO2dFQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O2tFQUlSLDhEQUFDSzt3REFBRTFCLFdBQVU7a0VBQXFCOzs7Ozs7Ozs7Ozs7Ozs7OztzREFHdEMsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDdUM7NERBQUd2QyxXQUFVO3NFQUF3Qzs7Ozs7O3NFQUN0RCw4REFBQ3lCOzREQUFLekIsV0FBVTtzRUFBa0M7Ozs7Ozs7Ozs7Ozs4REFFcEQsOERBQUMwQjtvREFBRTFCLFdBQVU7OERBQWtCOzs7Ozs7OERBQy9CLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN5QjtzRUFBSzs7Ozs7O3NFQUNOLDhEQUFDQTtzRUFBSzs7Ozs7O3NFQUNOLDhEQUFDQTtzRUFBSzs7Ozs7Ozs7Ozs7OzhEQUVSLDhEQUFDbkMseURBQU1BO29EQUFDa0QsU0FBUTtvREFBVXhDLFdBQVU7OERBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPbkQsOERBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDVix5REFBTUE7Z0NBQUNtRCxNQUFLOzBDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU14Qiw4REFBQ25CO2dCQUFRdEIsV0FBVTswQkFDakIsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDc0M7b0NBQUd0QyxXQUFVOzhDQUFzRDs7Ozs7OzhDQUNwRSw4REFBQzBCO29DQUFFMUIsV0FBVTs4Q0FBdUM7Ozs7Ozs7Ozs7OztzQ0FNdEQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2E7Z0RBQUliLFdBQVU7Z0RBQXFCYyxNQUFLO2dEQUFPQyxTQUFRO2dEQUFZQyxRQUFPOzBEQUN6RSw0RUFBQ0M7b0RBQ0NDLGVBQWM7b0RBQ2RDLGdCQUFlO29EQUNmQyxhQUFhO29EQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUlSLDhEQUFDa0I7NENBQUd2QyxXQUFVO3NEQUE2Qzs7Ozs7O3NEQUMzRCw4REFBQzBCOzRDQUFFMUIsV0FBVTtzREFBYTs7Ozs7Ozs7Ozs7OzhDQU81Qiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2E7Z0RBQUliLFdBQVU7Z0RBQXFCYyxNQUFLO2dEQUFPQyxTQUFRO2dEQUFZQyxRQUFPOzBEQUN6RSw0RUFBQ0M7b0RBQ0NDLGVBQWM7b0RBQ2RDLGdCQUFlO29EQUNmQyxhQUFhO29EQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUlSLDhEQUFDa0I7NENBQUd2QyxXQUFVO3NEQUE2Qzs7Ozs7O3NEQUMzRCw4REFBQzBCOzRDQUFFMUIsV0FBVTtzREFBYTs7Ozs7Ozs7Ozs7OzhDQU81Qiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2E7Z0RBQUliLFdBQVU7Z0RBQXFCYyxNQUFLO2dEQUFPQyxTQUFRO2dEQUFZQyxRQUFPOzBEQUN6RSw0RUFBQ0M7b0RBQ0NDLGVBQWM7b0RBQ2RDLGdCQUFlO29EQUNmQyxhQUFhO29EQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUlSLDhEQUFDa0I7NENBQUd2QyxXQUFVO3NEQUE2Qzs7Ozs7O3NEQUMzRCw4REFBQzBCOzRDQUFFMUIsV0FBVTtzREFBYTs7Ozs7Ozs7Ozs7OzhDQU81Qiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2E7Z0RBQUliLFdBQVU7Z0RBQXFCYyxNQUFLO2dEQUFPQyxTQUFRO2dEQUFZQyxRQUFPOzBEQUN6RSw0RUFBQ0M7b0RBQ0NDLGVBQWM7b0RBQ2RDLGdCQUFlO29EQUNmQyxhQUFhO29EQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUlSLDhEQUFDa0I7NENBQUd2QyxXQUFVO3NEQUE2Qzs7Ozs7O3NEQUMzRCw4REFBQzBCOzRDQUFFMUIsV0FBVTtzREFBYTs7Ozs7Ozs7Ozs7OzhDQU81Qiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2E7Z0RBQUliLFdBQVU7Z0RBQXFCYyxNQUFLO2dEQUFPQyxTQUFRO2dEQUFZQyxRQUFPOztrRUFDekUsOERBQUNDO3dEQUNDQyxlQUFjO3dEQUNkQyxnQkFBZTt3REFDZkMsYUFBYTt3REFDYkMsR0FBRTs7Ozs7O2tFQUVKLDhEQUFDSjt3REFDQ0MsZUFBYzt3REFDZEMsZ0JBQWU7d0RBQ2ZDLGFBQWE7d0RBQ2JDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUlSLDhEQUFDa0I7NENBQUd2QyxXQUFVO3NEQUE2Qzs7Ozs7O3NEQUMzRCw4REFBQzBCOzRDQUFFMUIsV0FBVTtzREFBYTs7Ozs7Ozs7Ozs7OzhDQU81Qiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2E7Z0RBQUliLFdBQVU7Z0RBQXFCYyxNQUFLO2dEQUFPQyxTQUFRO2dEQUFZQyxRQUFPOzBEQUN6RSw0RUFBQ0M7b0RBQ0NDLGVBQWM7b0RBQ2RDLGdCQUFlO29EQUNmQyxhQUFhO29EQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUlSLDhEQUFDa0I7NENBQUd2QyxXQUFVO3NEQUE2Qzs7Ozs7O3NEQUMzRCw4REFBQzBCOzRDQUFFMUIsV0FBVTtzREFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBVWxDLDhEQUFDMEM7Z0JBQU8xQyxXQUFVOzBCQUNoQiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEOztzREFDQyw4REFBQ0E7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNmLGtEQUFLQTtnREFDSmtCLEtBQUk7Z0RBQ0pDLEtBQUk7Z0RBQ0pDLE9BQU87Z0RBQ1BDLFFBQVE7Z0RBQ1JOLFdBQVU7Ozs7Ozs7Ozs7O3NEQUdkLDhEQUFDMEI7NENBQUUxQixXQUFVO3NEQUFxQjs7Ozs7O3NEQUlsQyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDMkM7b0RBQUV6QyxNQUFLO29EQUFJRixXQUFVO29EQUFxRFcsY0FBVzs4REFDcEYsNEVBQUNFO3dEQUFJYixXQUFVO3dEQUFVYyxNQUFLO3dEQUFlQyxTQUFRO2tFQUNuRCw0RUFBQ0U7NERBQUtJLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OERBR1osOERBQUNzQjtvREFBRXpDLE1BQUs7b0RBQUlGLFdBQVU7b0RBQXFEVyxjQUFXOzhEQUNwRiw0RUFBQ0U7d0RBQUliLFdBQVU7d0RBQVVjLE1BQUs7d0RBQWVDLFNBQVE7a0VBQ25ELDRFQUFDRTs0REFBS0ksR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4REFHWiw4REFBQ3NCO29EQUFFekMsTUFBSztvREFBSUYsV0FBVTtvREFBcURXLGNBQVc7OERBQ3BGLDRFQUFDRTt3REFBSWIsV0FBVTt3REFBVWMsTUFBSzt3REFBZUMsU0FBUTtrRUFDbkQsNEVBQUNFOzREQUFLSSxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzhEQUdaLDhEQUFDc0I7b0RBQUV6QyxNQUFLO29EQUFJRixXQUFVO29EQUFxRFcsY0FBVzs4REFDcEYsNEVBQUNFO3dEQUFJYixXQUFVO3dEQUFVYyxNQUFLO3dEQUFlQyxTQUFRO2tFQUNuRCw0RUFBQ0U7NERBQUtJLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBT2hCLDhEQUFDdEI7O3NEQUNDLDhEQUFDd0M7NENBQUd2QyxXQUFVO3NEQUE2Qjs7Ozs7O3NEQUMzQyw4REFBQzRDOzRDQUFHNUMsV0FBVTs7OERBQ1osOERBQUM2Qzs4REFDQyw0RUFBQ0Y7d0RBQUV6QyxNQUFLO3dEQUFPRixXQUFVO2tFQUFxRDs7Ozs7Ozs7Ozs7OERBSWhGLDhEQUFDNkM7OERBQ0MsNEVBQUNGO3dEQUFFekMsTUFBSzt3REFBUUYsV0FBVTtrRUFBcUQ7Ozs7Ozs7Ozs7OzhEQUlqRiw4REFBQzZDOzhEQUNDLDRFQUFDRjt3REFBRXpDLE1BQUs7d0RBQVFGLFdBQVU7a0VBQXFEOzs7Ozs7Ozs7Ozs4REFJakYsOERBQUM2Qzs4REFDQyw0RUFBQ0Y7d0RBQUV6QyxNQUFLO3dEQUFVRixXQUFVO2tFQUFxRDs7Ozs7Ozs7Ozs7OERBSW5GLDhEQUFDNkM7OERBQ0MsNEVBQUNGO3dEQUFFekMsTUFBSzt3REFBVUYsV0FBVTtrRUFBcUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVF2Riw4REFBQ0Q7O3NEQUNDLDhEQUFDd0M7NENBQUd2QyxXQUFVO3NEQUE2Qjs7Ozs7O3NEQUMzQyw4REFBQzRDOzRDQUFHNUMsV0FBVTs7OERBQ1osOERBQUM2Qzs4REFDQyw0RUFBQ0Y7d0RBQUV6QyxNQUFLO3dEQUFhRixXQUFVO2tFQUFxRDs7Ozs7Ozs7Ozs7OERBSXRGLDhEQUFDNkM7OERBQ0MsNEVBQUNGO3dEQUFFekMsTUFBSzt3REFBYUYsV0FBVTtrRUFBcUQ7Ozs7Ozs7Ozs7OzhEQUl0Riw4REFBQzZDOzhEQUNDLDRFQUFDRjt3REFBRXpDLE1BQUs7d0RBQVNGLFdBQVU7a0VBQXFEOzs7Ozs7Ozs7Ozs4REFJbEYsOERBQUM2Qzs4REFDQyw0RUFBQ0Y7d0RBQUV6QyxNQUFLO3dEQUFjRixXQUFVO2tFQUFxRDs7Ozs7Ozs7Ozs7OERBSXZGLDhEQUFDNkM7OERBQ0MsNEVBQUNGO3dEQUFFekMsTUFBSzt3REFBYUYsV0FBVTtrRUFBcUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVExRiw4REFBQ0Q7O3NEQUNDLDhEQUFDd0M7NENBQUd2QyxXQUFVO3NEQUE2Qjs7Ozs7O3NEQUMzQyw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNhOzREQUFJYixXQUFVOzREQUE0QmMsTUFBSzs0REFBT0MsU0FBUTs0REFBWUMsUUFBTzs7OEVBQ2hGLDhEQUFDQztvRUFDQ0MsZUFBYztvRUFDZEMsZ0JBQWU7b0VBQ2ZDLGFBQWE7b0VBQ2JDLEdBQUU7Ozs7Ozs4RUFFSiw4REFBQ0o7b0VBQ0NDLGVBQWM7b0VBQ2RDLGdCQUFlO29FQUNmQyxhQUFhO29FQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7c0VBR04sOERBQUNJOzREQUFLekIsV0FBVTs7Z0VBQWdCOzhFQUU5Qiw4REFBQzhDOzs7OztnRUFBSzs7Ozs7Ozs7Ozs7Ozs4REFJViw4REFBQy9DO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2E7NERBQUliLFdBQVU7NERBQTRCYyxNQUFLOzREQUFPQyxTQUFROzREQUFZQyxRQUFPO3NFQUNoRiw0RUFBQ0M7Z0VBQ0NDLGVBQWM7Z0VBQ2RDLGdCQUFlO2dFQUNmQyxhQUFhO2dFQUNiQyxHQUFFOzs7Ozs7Ozs7OztzRUFHTiw4REFBQ0k7NERBQUt6QixXQUFVO3NFQUFnQjs7Ozs7Ozs7Ozs7OzhEQUVsQyw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDYTs0REFBSWIsV0FBVTs0REFBNEJjLE1BQUs7NERBQU9DLFNBQVE7NERBQVlDLFFBQU87c0VBQ2hGLDRFQUFDQztnRUFDQ0MsZUFBYztnRUFDZEMsZ0JBQWU7Z0VBQ2ZDLGFBQWE7Z0VBQ2JDLEdBQUU7Ozs7Ozs7Ozs7O3NFQUdOLDhEQUFDSTs0REFBS3pCLFdBQVU7c0VBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTXhDLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQzBCO2dDQUFFMUIsV0FBVTs7b0NBQWdCO2tEQUUzQiw4REFBQzJDO3dDQUFFekMsTUFBSzt3Q0FBV0YsV0FBVTtrREFBNEM7Ozs7OztvQ0FFcEU7b0NBQUk7a0RBRVQsOERBQUMyQzt3Q0FBRXpDLE1BQUs7d0NBQVNGLFdBQVU7a0RBQTRDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU3JGO0dBNXFCd0JUOztRQUNNSiwwREFBT0E7UUFDcEJDLHNEQUFTQTs7O0tBRkZHIiwic291cmNlcyI6WyJFOlxcQ29kZVxcUG9ydGZvbGlvXFxOZXdNUkhcXGZyb250ZW5kXFxzcmNcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBJbWFnZSBmcm9tICduZXh0L2ltYWdlJ1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvQnV0dG9uJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICBjb25zdCB7IGlzQXV0aGVudGljYXRlZCB9ID0gdXNlQXV0aCgpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG4gIGNvbnN0IFtpc01vYmlsZU1lbnVPcGVuLCBzZXRJc01vYmlsZU1lbnVPcGVuXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKVxuXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzQXV0aGVudGljYXRlZCkge1xuICAgICAgcm91dGVyLnB1c2goJy9kYXNoYm9hcmQnKVxuICAgIH1cbiAgfSwgW2lzQXV0aGVudGljYXRlZCwgcm91dGVyXSlcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWJhY2tncm91bmQgdGV4dC1mb3JlZ3JvdW5kXCI+XG4gICAgICB7LyogTmF2aWdhdGlvbiAqL31cbiAgICAgIDxuYXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgc2hhZG93LXNtIGJvcmRlci1iIGJvcmRlci1ib3JkZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIGgtMTZcIj5cbiAgICAgICAgICAgIHsvKiBMb2dvICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL2xvZ28ucG5nXCJcbiAgICAgICAgICAgICAgICAgIGFsdD1cIk15UmVhbEh1YiBMb2dvXCJcbiAgICAgICAgICAgICAgICAgIHdpZHRoPXsyNTl9XG4gICAgICAgICAgICAgICAgICBoZWlnaHQ9ezY0fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC0xNiB3LWF1dG9cIlxuICAgICAgICAgICAgICAgICAgcHJpb3JpdHlcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIERlc2t0b3AgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtOFwiPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cInRleHQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnMgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICBIb21lXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNhYm91dC11c1wiIGNsYXNzTmFtZT1cInRleHQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnMgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICBBYm91dCBVc1xuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjcHJvcGVydGllc1wiIGNsYXNzTmFtZT1cInRleHQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnMgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICBQcm9wZXJ0aWVzXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNleHBsb3JlLXBsYWNlc1wiIGNsYXNzTmFtZT1cInRleHQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnMgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICBFeHBsb3JlIFBsYWNlc1xuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjZGV2ZWxvcGVyc1wiIGNsYXNzTmFtZT1cInRleHQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnMgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICBEZXZlbG9wZXJzXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogVXNlciBBY3Rpb25zICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hdXRoXCIgY2xhc3NOYW1lPVwidGV4dC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9ycyBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgIFNpZ24gSW5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGhyZWY9XCIvYXV0aFwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBiZy1wcmltYXJ5IHRleHQtd2hpdGUgaG92ZXI6YmctcHJpbWFyeS1kYXJrIGZvY3VzOnJpbmctcHJpbWFyeSBweC00IHB5LTIgdGV4dC1zbSByb3VuZGVkLW1kXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIEdldCBTdGFydGVkXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTW9iaWxlIG1lbnUgYnV0dG9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZDpoaWRkZW5cIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlTWVudU9wZW4oIWlzTW9iaWxlTWVudU9wZW4pfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnRleHQtcHJpbWFyeVwiXG4gICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIlRvZ2dsZSBtZW51XCJcbiAgICAgICAgICAgICAgICBhcmlhLWV4cGFuZGVkPXtpc01vYmlsZU1lbnVPcGVufVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzTW9iaWxlTWVudU9wZW4gPyAoXG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImgtNiB3LTZcIiBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTYgMThMMTggNk02IDZsMTIgMTJcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwiaC02IHctNlwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNCA2aDE2TTQgMTJoMTZNNCAxOGgxNlwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTW9iaWxlIE1lbnUgKi99XG4gICAgICAgIHtpc01vYmlsZU1lbnVPcGVuICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmhpZGRlbiBiZy13aGl0ZSBib3JkZXItdCBib3JkZXItYm9yZGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTQgcHktMiBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICBocmVmPVwiL1wiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtMyBweS0yIHRleHQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgaG92ZXI6YmctaW5wdXQgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNb2JpbGVNZW51T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBIb21lXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICBocmVmPVwiI2Fib3V0LXVzXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC0zIHB5LTIgdGV4dC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSBob3ZlcjpiZy1pbnB1dCByb3VuZGVkLW1kIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01vYmlsZU1lbnVPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIEFib3V0IFVzXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICBocmVmPVwiI3Byb3BlcnRpZXNcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTMgcHktMiB0ZXh0LWZvcmVncm91bmQgaG92ZXI6dGV4dC1wcmltYXJ5IGhvdmVyOmJnLWlucHV0IHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlTWVudU9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgUHJvcGVydGllc1xuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgaHJlZj1cIiNleHBsb3JlLXBsYWNlc1wiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtMyBweS0yIHRleHQtZm9yZWdyb3VuZCBob3Zlcjp0ZXh0LXByaW1hcnkgaG92ZXI6YmctaW5wdXQgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNb2JpbGVNZW51T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBFeHBsb3JlIFBsYWNlc1xuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgaHJlZj1cIiNkZXZlbG9wZXJzXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC0zIHB5LTIgdGV4dC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSBob3ZlcjpiZy1pbnB1dCByb3VuZGVkLW1kIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01vYmlsZU1lbnVPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIERldmVsb3BlcnNcbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1ib3JkZXIgcHQtMiBtdC0yXCI+XG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvYXV0aFwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC0zIHB5LTIgdGV4dC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSBob3ZlcjpiZy1pbnB1dCByb3VuZGVkLW1kIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTW9iaWxlTWVudU9wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIFNpZ24gSW5cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIvYXV0aFwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayBweC0zIHB5LTIgbXQtMSBiZy1wcmltYXJ5IHRleHQtd2hpdGUgaG92ZXI6YmctcHJpbWFyeS1kYXJrIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnMgdGV4dC1jZW50ZXJcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNb2JpbGVNZW51T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgR2V0IFN0YXJ0ZWRcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L25hdj5cblxuICAgICAgey8qIEhlcm8gU2VjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInJlbGF0aXZlIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHJpbWFyeS1saWdodCB0by1ncmVlbi01MCBweS0yMCBsZzpweS0zMlwiIHJvbGU9XCJiYW5uZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBsZzpncmlkLWNvbHMtMiBnYXAtMTIgaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc2xpZGUtdXBcIj5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNHhsIHNtOnRleHQtNXhsIGxnOnRleHQtNnhsIGZvbnQtYm9sZCB0ZXh0LWZvcmVncm91bmQgbWItNlwiPlxuICAgICAgICAgICAgICAgIEZpbmQgWW91clxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcHJpbWFyeSBibG9ja1wiPkRyZWFtIEhvbWU8L3NwYW4+XG4gICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgc206dGV4dC14bCB0ZXh0LW11dGVkIG1iLTggbWF4LXctbGdcIj5cbiAgICAgICAgICAgICAgICBEaXNjb3ZlciB0aGUgcGVyZmVjdCBwcm9wZXJ0eSB3aXRoIG91ciBjb21wcmVoZW5zaXZlIHJlYWwgZXN0YXRlIHBsYXRmb3JtLiBCdXksIHNlbGwsIG9yIHJlbnQgd2l0aFxuICAgICAgICAgICAgICAgIGNvbmZpZGVuY2UgdXNpbmcgb3VyIGV4cGVydCB0b29scyBhbmQgc2VydmljZXMuXG4gICAgICAgICAgICAgIDwvcD5cblxuICAgICAgICAgICAgICB7LyogUHJvcGVydHkgU2VhcmNoIEZvcm0gKi99XG4gICAgICAgICAgICAgIDxmb3JtXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctY2FyZCBwLTYgcm91bmRlZC1sZyBzaGFkb3ctbGcgYm9yZGVyIGJvcmRlci1ib3JkZXIgYW5pbWF0ZS1zY2FsZS1pbiBob3Zlci1nbG93XCJcbiAgICAgICAgICAgICAgICByb2xlPVwic2VhcmNoXCJcbiAgICAgICAgICAgICAgICBhcmlhLWxhYmVsPVwiUHJvcGVydHkgc2VhcmNoXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJsb2NhdGlvblwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1mb3JlZ3JvdW5kIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICBMb2NhdGlvblxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgaWQ9XCJsb2NhdGlvblwiXG4gICAgICAgICAgICAgICAgICAgICAgbmFtZT1cImxvY2F0aW9uXCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGNpdHkgb3IgemlwIGNvZGVcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ib3JkZXIgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeSBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgIGFyaWEtZGVzY3JpYmVkYnk9XCJsb2NhdGlvbi1oZWxwXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gaWQ9XCJsb2NhdGlvbi1oZWxwXCIgY2xhc3NOYW1lPVwic3Itb25seVwiPlxuICAgICAgICAgICAgICAgICAgICAgIEVudGVyIHRoZSBjaXR5IG9yIHppcCBjb2RlIHdoZXJlIHlvdSB3YW50IHRvIHNlYXJjaCBmb3IgcHJvcGVydGllc1xuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwicHJvcGVydHktdHlwZVwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1mb3JlZ3JvdW5kIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICBQcm9wZXJ0eSBUeXBlXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cInByb3BlcnR5LXR5cGVcIlxuICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJwcm9wZXJ0eVR5cGVcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ib3JkZXIgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeSBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgIGFyaWEtZGVzY3JpYmVkYnk9XCJwcm9wZXJ0eS10eXBlLWhlbHBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPkFsbCBUeXBlczwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJob3VzZVwiPkhvdXNlPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFwYXJ0bWVudFwiPkFwYXJ0bWVudDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjb25kb1wiPkNvbmRvPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInRvd25ob3VzZVwiPlRvd25ob3VzZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gaWQ9XCJwcm9wZXJ0eS10eXBlLWhlbHBcIiBjbGFzc05hbWU9XCJzci1vbmx5XCI+XG4gICAgICAgICAgICAgICAgICAgICAgU2VsZWN0IHRoZSB0eXBlIG9mIHByb3BlcnR5IHlvdSBhcmUgbG9va2luZyBmb3JcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cInByaWNlLXJhbmdlXCIgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWZvcmVncm91bmQgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIFByaWNlIFJhbmdlXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICBpZD1cInByaWNlLXJhbmdlXCJcbiAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwicHJpY2VSYW5nZVwiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWJvcmRlciByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5IGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgYXJpYS1kZXNjcmliZWRieT1cInByaWNlLXJhbmdlLWhlbHBcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPkFueSBQcmljZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCIwLTIwMDAwMFwiPlVuZGVyICQyMDBLPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjIwMDAwMC00MDAwMDBcIj4kMjAwSyAtICQ0MDBLPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjQwMDAwMC02MDAwMDBcIj4kNDAwSyAtICQ2MDBLPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjYwMDAwMCtcIj4kNjAwSys8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGlkPVwicHJpY2UtcmFuZ2UtaGVscFwiIGNsYXNzTmFtZT1cInNyLW9ubHlcIj5cbiAgICAgICAgICAgICAgICAgICAgICBTZWxlY3QgeW91ciBwcmVmZXJyZWQgcHJpY2UgcmFuZ2UgZm9yIHByb3BlcnRpZXNcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtZW5kXCI+XG4gICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdHlwZT1cInN1Ym1pdFwiIGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHlcIj5TZWFyY2ggZm9yPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIFNlYXJjaCBQcm9wZXJ0aWVzXG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZm9ybT5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogSGVybyBJbWFnZSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhc3BlY3QtWzQvM10gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1wcmltYXJ5LzIwIHRvLXByaW1hcnkvMTAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yNCBoLTI0IGJnLXByaW1hcnkvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMTIgaC0xMiB0ZXh0LXByaW1hcnlcIiBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTMgMTJsMi0ybTAgMGw3LTcgNyA3TTUgMTB2MTBhMSAxIDAgMDAxIDFoM20xMC0xMWwyIDJtLTItMnYxMGExIDEgMCAwMS0xIDFoLTNtLTYgMGExIDEgMCAwMDEtMXYtNGExIDEgMCAwMTEtMWgyYTEgMSAwIDAxMSAxdjRhMSAxIDAgMDAxIDFtLTYgMGg2XCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZFwiPkJlYXV0aWZ1bCBQcm9wZXJ0eSBJbWFnZTwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBGZWF0dXJlZCBQcm9wZXJ0aWVzIFNlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xNiBsZzpweS0yNCBiZy1jYXJkXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbGc6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtZm9yZWdyb3VuZCBtYi00XCI+RmVhdHVyZWQgUHJvcGVydGllczwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtbXV0ZWQgbWF4LXctMnhsIG14LWF1dG9cIj5cbiAgICAgICAgICAgICAgRGlzY292ZXIgb3VyIGhhbmRwaWNrZWQgc2VsZWN0aW9uIG9mIHByZW1pdW0gcHJvcGVydGllcyBhdmFpbGFibGUgZm9yIHNhbGUgYW5kIHJlbnRcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtOFwiPlxuICAgICAgICAgICAgey8qIFByb3BlcnR5IENhcmQgMSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctY2FyZCByb3VuZGVkLWxnIHNoYWRvdy1sZyBib3JkZXIgYm9yZGVyLWJvcmRlciBvdmVyZmxvdy1oaWRkZW4gaG92ZXItbGlmdCBhbmltYXRlLWZhZGUtaW4gZ3JvdXAgY3Vyc29yLXBvaW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhc3BlY3QtWzQvM10gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1wcmltYXJ5LzEwIHRvLXByaW1hcnkvNSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLXByaW1hcnkvMjAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1wcmltYXJ5XCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk0zIDEybDItMm0wIDBsNy03IDcgN001IDEwdjEwYTEgMSAwIDAwMSAxaDNtMTAtMTFsMiAybS0yLTJ2MTBhMSAxIDAgMDEtMSAxaC0zbS02IDBhMSAxIDAgMDAxLTF2LTRhMSAxIDAgMDExLTFoMmExIDEgMCAwMTEgMXY0YTEgMSAwIDAwMSAxbS02IDBoNlwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZFwiPlByb3BlcnR5IEltYWdlPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0IG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3JlZ3JvdW5kXCI+TW9kZXJuIEZhbWlseSBIb21lPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXByaW1hcnlcIj4kNDg1LDAwMDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkIG1iLTRcIj4xMjMgT2FrIFN0cmVldCwgRG93bnRvd24gRGlzdHJpY3Q8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtIHRleHQtbXV0ZWQgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+8J+bj++4jyA0IEJlZHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj7wn5q/IDMgQmF0aHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj7wn5OQIDIsNDAwIHNxIGZ0PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgIFZpZXcgRGV0YWlsc1xuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogUHJvcGVydHkgQ2FyZCAyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1jYXJkIHJvdW5kZWQtbGcgc2hhZG93LWxnIGJvcmRlciBib3JkZXItYm9yZGVyIG92ZXJmbG93LWhpZGRlbiBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1zaGFkb3cgZHVyYXRpb24tMzAwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXNwZWN0LVs0LzNdIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHJpbWFyeS8xMCB0by1wcmltYXJ5LzUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1wcmltYXJ5LzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtcHJpbWFyeVwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMTkgMjFWNWEyIDIgMCAwMC0yLTJIN2EyIDIgMCAwMC0yIDJ2MTZtMTQgMGgybS0yIDBoLTVtLTkgMEgzbTIgMGg1TTkgN2gxbS0xIDRoMW00LTRoMW0tMSA0aDFtLTUgMTB2LTVhMSAxIDAgMDExLTFoMmExIDEgMCAwMTEgMXY1bS00IDBoNFwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZFwiPlByb3BlcnR5IEltYWdlPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0IG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3JlZ3JvdW5kXCI+THV4dXJ5IEFwYXJ0bWVudDwvaDM+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wcmltYXJ5XCI+JDIsODAwL21vPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQgbWItNFwiPjQ1NiBQaW5lIEF2ZW51ZSwgVXB0b3duIEFyZWE8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtIHRleHQtbXV0ZWQgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+8J+bj++4jyAyIEJlZHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj7wn5q/IDIgQmF0aHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj7wn5OQIDEsMjAwIHNxIGZ0PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgIFZpZXcgRGV0YWlsc1xuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogUHJvcGVydHkgQ2FyZCAzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1jYXJkIHJvdW5kZWQtbGcgc2hhZG93LWxnIGJvcmRlciBib3JkZXItYm9yZGVyIG92ZXJmbG93LWhpZGRlbiBob3ZlcjpzaGFkb3cteGwgdHJhbnNpdGlvbi1zaGFkb3cgZHVyYXRpb24tMzAwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYXNwZWN0LVs0LzNdIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tcHJpbWFyeS8xMCB0by1wcmltYXJ5LzUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1wcmltYXJ5LzIwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtcHJpbWFyeVwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNOCAxNHYzbTQtM3YzbTQtM3YzTTMgMjFoMThNMyAxMGgxOE0zIDdsOS00IDkgNE00IDEwdjExTTIwIDEwdjExXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkXCI+UHJvcGVydHkgSW1hZ2U8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWZvcmVncm91bmRcIj5Db3p5IFRvd25ob3VzZTwvaDM+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wcmltYXJ5XCI+JDMyNSwwMDA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZCBtYi00XCI+Nzg5IE1hcGxlIExhbmUsIFN1YnVyYmFuIEhlaWdodHM8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtIHRleHQtbXV0ZWQgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+8J+bj++4jyAzIEJlZHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj7wn5q/IDIgQmF0aHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj7wn5OQIDEsODAwIHNxIGZ0PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgIFZpZXcgRGV0YWlsc1xuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtdC0xMlwiPlxuICAgICAgICAgICAgPEJ1dHRvbiBzaXplPVwibGdcIj5WaWV3IEFsbCBQcm9wZXJ0aWVzPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogS2V5IEZlYXR1cmVzIFNlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0xNiBsZzpweS0yNCBiZy1iYWNrZ3JvdW5kXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTEyXCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbGc6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtZm9yZWdyb3VuZCBtYi00XCI+V2h5IENob29zZSBNeVJlYWxIdWI/PC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgdGV4dC1tdXRlZCBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICBFeHBlcmllbmNlIHRoZSBmdXR1cmUgb2YgcmVhbCBlc3RhdGUgd2l0aCBvdXIgY29tcHJlaGVuc2l2ZSBwbGF0Zm9ybSBkZXNpZ25lZCBmb3IgbW9kZXJuIHByb3BlcnR5XG4gICAgICAgICAgICAgIHRyYW5zYWN0aW9uc1xuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC04XCI+XG4gICAgICAgICAgICB7LyogRmVhdHVyZSAxICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctcHJpbWFyeSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtd2hpdGVcIiBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgZD1cIk0yMSAyMWwtNi02bTItNWE3IDcgMCAxMS0xNCAwIDcgNyAwIDAxMTQgMHpcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3JlZ3JvdW5kIG1iLTNcIj5GaW5kIFlvdXIgRHJlYW0gSG9tZTwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWRcIj5cbiAgICAgICAgICAgICAgICBBZHZhbmNlZCBzZWFyY2ggZmlsdGVycyBhbmQgQUktcG93ZXJlZCByZWNvbW1lbmRhdGlvbnMgaGVscCB5b3UgZGlzY292ZXIgdGhlIHBlcmZlY3QgcHJvcGVydHkgdGhhdFxuICAgICAgICAgICAgICAgIG1hdGNoZXMgeW91ciBsaWZlc3R5bGUgYW5kIGJ1ZGdldC5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBGZWF0dXJlIDIgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1wcmltYXJ5IHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBteC1hdXRvIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC13aGl0ZVwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICBkPVwiTTE3IDIwaDV2LTJhMyAzIDAgMDAtNS4zNTYtMS44NTdNMTcgMjBIN20xMCAwdi0yYzAtLjY1Ni0uMTI2LTEuMjgzLS4zNTYtMS44NTdNNyAyMEgydi0yYTMgMyAwIDAxNS4zNTYtMS44NTdNNyAyMHYtMmMwLS42NTYuMTI2LTEuMjgzLjM1Ni0xLjg1N20wIDBhNS4wMDIgNS4wMDIgMCAwMTkuMjg4IDBNMTUgN2EzIDMgMCAxMS02IDAgMyAzIDAgMDE2IDB6bTYgM2EyIDIgMCAxMS00IDAgMiAyIDAgMDE0IDB6TTcgMTBhMiAyIDAgMTEtNCAwIDIgMiAwIDAxNCAwelwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWZvcmVncm91bmQgbWItM1wiPkV4cGVydCBBZ2VudHM8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkXCI+XG4gICAgICAgICAgICAgICAgQ29ubmVjdCB3aXRoIGNlcnRpZmllZCByZWFsIGVzdGF0ZSBwcm9mZXNzaW9uYWxzIHdobyBwcm92aWRlIHBlcnNvbmFsaXplZCBndWlkYW5jZSB0aHJvdWdob3V0IHlvdXJcbiAgICAgICAgICAgICAgICBidXlpbmcgb3Igc2VsbGluZyBqb3VybmV5LlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEZlYXR1cmUgMyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLXByaW1hcnkgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXdoaXRlXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgIGQ9XCJNOSAxOXYtNmEyIDIgMCAwMC0yLTJINWEyIDIgMCAwMC0yIDJ2NmEyIDIgMCAwMDIgMmgyYTIgMiAwIDAwMi0yem0wIDBWOWEyIDIgMCAwMTItMmgyYTIgMiAwIDAxMiAydjEwbS02IDBhMiAyIDAgMDAyIDJoMmEyIDIgMCAwMDItMm0wIDBWNWEyIDIgMCAwMTItMmgyYTIgMiAwIDAxMiAydjE0YTIgMiAwIDAxLTIgMmgtMmEyIDIgMCAwMS0yLTJ6XCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZm9yZWdyb3VuZCBtYi0zXCI+TWFya2V0IEFuYWx5dGljczwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWRcIj5cbiAgICAgICAgICAgICAgICBBY2Nlc3MgcmVhbC10aW1lIG1hcmtldCBkYXRhLCBwcmljZSB0cmVuZHMsIGFuZCBuZWlnaGJvcmhvb2QgaW5zaWdodHMgdG8gbWFrZSBpbmZvcm1lZCBkZWNpc2lvbnMgd2l0aFxuICAgICAgICAgICAgICAgIGNvbmZpZGVuY2UuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogRmVhdHVyZSA0ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctcHJpbWFyeSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtd2hpdGVcIiBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgZD1cIk0xMiAxNXYybS02IDRoMTJhMiAyIDAgMDAyLTJ2LTZhMiAyIDAgMDAtMi0ySDZhMiAyIDAgMDAtMiAydjZhMiAyIDAgMDAyIDJ6bTEwLTEwVjdhNCA0IDAgMDAtOCAwdjRoOHpcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3JlZ3JvdW5kIG1iLTNcIj5TZWN1cmUgVHJhbnNhY3Rpb25zPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZFwiPlxuICAgICAgICAgICAgICAgIEJhbmstbGV2ZWwgc2VjdXJpdHkgYW5kIGVuY3J5cHRlZCBjb21tdW5pY2F0aW9ucyBlbnN1cmUgeW91ciBwZXJzb25hbCBpbmZvcm1hdGlvbiBhbmQgdHJhbnNhY3Rpb25zIGFyZVxuICAgICAgICAgICAgICAgIGFsd2F5cyBwcm90ZWN0ZWQuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogRmVhdHVyZSA1ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctcHJpbWFyeSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbXgtYXV0byBtYi00XCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTggaC04IHRleHQtd2hpdGVcIiBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgZD1cIk0xMC4zMjUgNC4zMTdjLjQyNi0xLjc1NiAyLjkyNC0xLjc1NiAzLjM1IDBhMS43MjQgMS43MjQgMCAwMDIuNTczIDEuMDY2YzEuNTQzLS45NCAzLjMxLjgyNiAyLjM3IDIuMzdhMS43MjQgMS43MjQgMCAwMDEuMDY1IDIuNTcyYzEuNzU2LjQyNiAxLjc1NiAyLjkyNCAwIDMuMzVhMS43MjQgMS43MjQgMCAwMC0xLjA2NiAyLjU3M2MuOTQgMS41NDMtLjgyNiAzLjMxLTIuMzcgMi4zN2ExLjcyNCAxLjcyNCAwIDAwLTIuNTcyIDEuMDY1Yy0uNDI2IDEuNzU2LTIuOTI0IDEuNzU2LTMuMzUgMGExLjcyNCAxLjcyNCAwIDAwLTIuNTczLTEuMDY2Yy0xLjU0My45NC0zLjMxLS44MjYtMi4zNy0yLjM3YTEuNzI0IDEuNzI0IDAgMDAtMS4wNjUtMi41NzJjLTEuNzU2LS40MjYtMS43NTYtMi45MjQgMC0zLjM1YTEuNzI0IDEuNzI0IDAgMDAxLjA2Ni0yLjU3M2MtLjk0LTEuNTQzLjgyNi0zLjMxIDIuMzctMi4zNy45OTYuNjA4IDIuMjk2LjA3IDIuNTcyLTEuMDY1elwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICBkPVwiTTE1IDEyYTMgMyAwIDExLTYgMCAzIDMgMCAwMTYgMHpcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3JlZ3JvdW5kIG1iLTNcIj5QcmVtaXVtIFNlcnZpY2U8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkXCI+XG4gICAgICAgICAgICAgICAgRW5qb3kgd2hpdGUtZ2xvdmUgc2VydmljZSB3aXRoIGRlZGljYXRlZCBzdXBwb3J0LCB2aXJ0dWFsIHRvdXJzLCBhbmQgcGVyc29uYWxpemVkIHByb3BlcnR5XG4gICAgICAgICAgICAgICAgcmVjb21tZW5kYXRpb25zLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEZlYXR1cmUgNiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xNiBoLTE2IGJnLXByaW1hcnkgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy04IGgtOCB0ZXh0LXdoaXRlXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgIGQ9XCJNMTIgOGMtMS42NTcgMC0zIC44OTUtMyAyczEuMzQzIDIgMyAyIDMgLjg5NSAzIDItMS4zNDMgMi0zIDJtMC04YzEuMTEgMCAyLjA4LjQwMiAyLjU5OSAxTTEyIDhWN20wIDF2OG0wIDB2MW0wLTFjLTEuMTEgMC0yLjA4LS40MDItMi41OTktMVwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWZvcmVncm91bmQgbWItM1wiPlNtYXJ0IEZpbmFuY2luZzwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWRcIj5cbiAgICAgICAgICAgICAgICBBY2Nlc3MgY29tcGV0aXRpdmUgbW9ydGdhZ2UgcmF0ZXMsIHByZS1hcHByb3ZhbCB0b29scywgYW5kIGZpbmFuY2luZyBvcHRpb25zIHRhaWxvcmVkIHRvIHlvdXIgZmluYW5jaWFsXG4gICAgICAgICAgICAgICAgc2l0dWF0aW9uLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBGb290ZXIgKi99XG4gICAgICA8Zm9vdGVyIGNsYXNzTmFtZT1cImJnLXNlY29uZGFyeSB0ZXh0LXdoaXRlIHB5LTEyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLThcIj5cbiAgICAgICAgICAgIHsvKiBDb21wYW55IEluZm8gKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICAgIHNyYz1cIi9pbWFnZXMvbG9nby5wbmdcIlxuICAgICAgICAgICAgICAgICAgYWx0PVwiTXlSZWFsSHViIExvZ29cIlxuICAgICAgICAgICAgICAgICAgd2lkdGg9ezEyOX1cbiAgICAgICAgICAgICAgICAgIGhlaWdodD17MzJ9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTggdy1hdXRvIGJyaWdodG5lc3MtMCBpbnZlcnRcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICBZb3VyIHRydXN0ZWQgcGFydG5lciBpbiByZWFsIGVzdGF0ZS4gV2UgaGVscCB5b3UgZmluZCwgYnV5LCBzZWxsLCBhbmQgcmVudCBwcm9wZXJ0aWVzIHdpdGggY29uZmlkZW5jZVxuICAgICAgICAgICAgICAgIGFuZCBlYXNlLlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCIgYXJpYS1sYWJlbD1cIkZhY2Vib29rXCI+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTZcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTI0IDEyLjA3M2MwLTYuNjI3LTUuMzczLTEyLTEyLTEycy0xMiA1LjM3My0xMiAxMmMwIDUuOTkgNC4zODggMTAuOTU0IDEwLjEyNSAxMS44NTR2LTguMzg1SDcuMDc4di0zLjQ3aDMuMDQ3VjkuNDNjMC0zLjAwNyAxLjc5Mi00LjY2OSA0LjUzMy00LjY2OSAxLjMxMiAwIDIuNjg2LjIzNSAyLjY4Ni4yMzV2Mi45NTNIMTUuODNjLTEuNDkxIDAtMS45NTYuOTI1LTEuOTU2IDEuODc0djIuMjVoMy4zMjhsLS41MzIgMy40N2gtMi43OTZ2OC4zODVDMTkuNjEyIDIzLjAyNyAyNCAxOC4wNjIgMjQgMTIuMDczelwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiIGFyaWEtbGFiZWw9XCJUd2l0dGVyXCI+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTZcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTIzLjk1MyA0LjU3YTEwIDEwIDAgMDEtMi44MjUuNzc1IDQuOTU4IDQuOTU4IDAgMDAyLjE2My0yLjcyM2MtLjk1MS41NTUtMi4wMDUuOTU5LTMuMTI3IDEuMTg0YTQuOTIgNC45MiAwIDAwLTguMzg0IDQuNDgyQzcuNjkgOC4wOTUgNC4wNjcgNi4xMyAxLjY0IDMuMTYyYTQuODIyIDQuODIyIDAgMDAtLjY2NiAyLjQ3NWMwIDEuNzEuODcgMy4yMTMgMi4xODggNC4wOTZhNC45MDQgNC45MDQgMCAwMS0yLjIyOC0uNjE2di4wNmE0LjkyMyA0LjkyMyAwIDAwMy45NDYgNC44MjcgNC45OTYgNC45OTYgMCAwMS0yLjIxMi4wODUgNC45MzYgNC45MzYgMCAwMDQuNjA0IDMuNDE3IDkuODY3IDkuODY3IDAgMDEtNi4xMDIgMi4xMDVjLS4zOSAwLS43NzktLjAyMy0xLjE3LS4wNjdhMTMuOTk1IDEzLjk5NSAwIDAwNy41NTcgMi4yMDljOS4wNTMgMCAxMy45OTgtNy40OTYgMTMuOTk4LTEzLjk4NSAwLS4yMSAwLS40Mi0uMDE1LS42M0E5LjkzNSA5LjkzNSAwIDAwMjQgNC41OXpcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIiBhcmlhLWxhYmVsPVwiTGlua2VkSW5cIj5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy02IGgtNlwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMjAuNDQ3IDIwLjQ1MmgtMy41NTR2LTUuNTY5YzAtMS4zMjgtLjAyNy0zLjAzNy0xLjg1Mi0zLjAzNy0xLjg1MyAwLTIuMTM2IDEuNDQ1LTIuMTM2IDIuOTM5djUuNjY3SDkuMzUxVjloMy40MTR2MS41NjFoLjA0NmMuNDc3LS45IDEuNjM3LTEuODUgMy4zNy0xLjg1IDMuNjAxIDAgNC4yNjcgMi4zNyA0LjI2NyA1LjQ1NXY2LjI4NnpNNS4zMzcgNy40MzNjLTEuMTQ0IDAtMi4wNjMtLjkyNi0yLjA2My0yLjA2NSAwLTEuMTM4LjkyLTIuMDYzIDIuMDYzLTIuMDYzIDEuMTQgMCAyLjA2NC45MjUgMi4wNjQgMi4wNjMgMCAxLjEzOS0uOTI1IDIuMDY1LTIuMDY0IDIuMDY1em0xLjc4MiAxMy4wMTlIMy41NTVWOWgzLjU2NHYxMS40NTJ6TTIyLjIyNSAwSDEuNzcxQy43OTIgMCAwIC43NzQgMCAxLjcyOXYyMC41NDJDMCAyMy4yMjcuNzkyIDI0IDEuNzcxIDI0aDIwLjQ1MUMyMy4yIDI0IDI0IDIzLjIyNyAyNCAyMi4yNzFWMS43MjlDMjQgLjc3NCAyMy4yIDAgMjIuMjIyIDBoLjAwM3pcIiAvPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIiBhcmlhLWxhYmVsPVwiSW5zdGFncmFtXCI+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNiBoLTZcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTEyLjAxNyAwQzUuMzk2IDAgLjAyOSA1LjM2Ny4wMjkgMTEuOTg3YzAgNi42MiA1LjM2NyAxMS45ODcgMTEuOTg4IDExLjk4NyA2LjYyIDAgMTEuOTg3LTUuMzY3IDExLjk4Ny0xMS45ODdDMjQuMDE0IDUuMzY3IDE4LjYzNy4wMDEgMTIuMDE3LjAwMXpNOC40NDkgMTYuOTg4Yy0xLjI5NyAwLTIuNDQ4LS40OS0zLjMyMy0xLjI5N0M0LjE5OCAxNC44OTUgMy43MDggMTMuNzQ0IDMuNzA4IDEyLjQ0N3MuNDktMi40NDggMS4yOTctMy4zMjNDNS45MDIgOC4xOTggNy4wNTMgNy43MDggOC4zNSA3LjcwOHMyLjQ0OC40OSAzLjMyMyAxLjI5N2MuODk3Ljg3NSAxLjM4NyAyLjAyNiAxLjM4NyAzLjMyM3MtLjQ5IDIuNDQ4LTEuMjk3IDMuMzIzYy0uODc1Ljg5Ny0yLjAyNiAxLjM4Ny0zLjMyMyAxLjM4N3ptNy43MTggMGMtMS4yOTcgMC0yLjQ0OC0uNDktMy4zMjMtMS4yOTctLjg5Ny0uODc1LTEuMzg3LTIuMDI2LTEuMzg3LTMuMzIzcy40OS0yLjQ0OCAxLjI5Ny0zLjMyM2MuODc1LS44OTcgMi4wMjYtMS4zODcgMy4zMjMtMS4zODdzMi40NDguNDkgMy4zMjMgMS4yOTdjLjg5Ny44NzUgMS4zODcgMi4wMjYgMS4zODcgMy4zMjNzLS40OSAyLjQ0OC0xLjI5NyAzLjMyM2MtLjg3NS44OTctMi4wMjYgMS4zODctMy4zMjMgMS4zODd6XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFF1aWNrIExpbmtzICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCBtYi00XCI+UXVpY2sgTGlua3M8L2gzPlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIiNidXlcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgICBCdXkgUHJvcGVydGllc1xuICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIiNzZWxsXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgU2VsbCBZb3VyIEhvbWVcbiAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjcmVudFwiIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgIFJlbnQgUHJvcGVydGllc1xuICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIiNhZ2VudHNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgICBGaW5kIEFnZW50c1xuICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIiNtYXJrZXRcIiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxuICAgICAgICAgICAgICAgICAgICBNYXJrZXQgSW5zaWdodHNcbiAgICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBTZXJ2aWNlcyAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPlNlcnZpY2VzPC9oMz5cbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIDxsaT5cbiAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjdmFsdWF0aW9uXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgUHJvcGVydHkgVmFsdWF0aW9uXG4gICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgICA8YSBocmVmPVwiI2ZpbmFuY2luZ1wiIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgIE1vcnRnYWdlIFNlcnZpY2VzXG4gICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgICA8YSBocmVmPVwiI2xlZ2FsXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgTGVnYWwgU3VwcG9ydFxuICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPlxuICAgICAgICAgICAgICAgICAgPGEgaHJlZj1cIiNpbnNwZWN0aW9uXCIgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LXByaW1hcnkgdHJhbnNpdGlvbi1jb2xvcnNcIj5cbiAgICAgICAgICAgICAgICAgICAgSG9tZSBJbnNwZWN0aW9uXG4gICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+XG4gICAgICAgICAgICAgICAgICA8YSBocmVmPVwiI2luc3VyYW5jZVwiIGNsYXNzTmFtZT1cInRleHQtZ3JheS0zMDAgaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgIFByb3BlcnR5IEluc3VyYW5jZVxuICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIENvbnRhY3QgSW5mbyAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItNFwiPkNvbnRhY3QgVXM8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXByaW1hcnkgbXItM1wiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgZD1cIk0xNy42NTcgMTYuNjU3TDEzLjQxNCAyMC45YTEuOTk4IDEuOTk4IDAgMDEtMi44MjcgMGwtNC4yNDQtNC4yNDNhOCA4IDAgMTExMS4zMTQgMHpcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgZD1cIk0xNSAxMWEzIDMgMCAxMS02IDAgMyAzIDAgMDE2IDB6XCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICAgICAgICAxMjMgUmVhbCBFc3RhdGUgQXZlXG4gICAgICAgICAgICAgICAgICAgIDxiciAvPlxuICAgICAgICAgICAgICAgICAgICBTdWl0ZSAxMDAsIENpdHksIFNUIDEyMzQ1XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtcHJpbWFyeSBtci0zXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICBkPVwiTTMgNWEyIDIgMCAwMTItMmgzLjI4YTEgMSAwIDAxLjk0OC42ODRsMS40OTggNC40OTNhMSAxIDAgMDEtLjUwMiAxLjIxbC0yLjI1NyAxLjEzYTExLjA0MiAxMS4wNDIgMCAwMDUuNTE2IDUuNTE2bDEuMTMtMi4yNTdhMSAxIDAgMDExLjIxLS41MDJsNC40OTMgMS40OThhMSAxIDAgMDEuNjg0Ljk0OVYxOWEyIDIgMCAwMS0yIDJoLTFDOS43MTYgMjEgMyAxNC4yODQgMyA2VjV6XCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMFwiPig1NTUpIDEyMy00NTY3PC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXByaW1hcnkgbXItM1wiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgZD1cIk0zIDhsNy44OSA0LjI2YTIgMiAwIDAwMi4yMiAwTDIxIDhNNSAxOWgxNGEyIDIgMCAwMDItMlY3YTIgMiAwIDAwLTItMkg1YTIgMiAwIDAwLTIgMnYxMGEyIDIgMCAwMDIgMnpcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+aW5mb0BteXJlYWxodWIuY29tPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZ3JheS02MDAgbXQtOCBwdC04IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgIMKpIDIwMjQgTXlSZWFsSHViLiBBbGwgcmlnaHRzIHJlc2VydmVkLiB8XG4gICAgICAgICAgICAgIDxhIGhyZWY9XCIjcHJpdmFjeVwiIGNsYXNzTmFtZT1cImhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9ycyBtbC0xXCI+XG4gICAgICAgICAgICAgICAgUHJpdmFjeSBQb2xpY3lcbiAgICAgICAgICAgICAgPC9hPnsnICd9XG4gICAgICAgICAgICAgIHxcbiAgICAgICAgICAgICAgPGEgaHJlZj1cIiN0ZXJtc1wiIGNsYXNzTmFtZT1cImhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9ycyBtbC0xXCI+XG4gICAgICAgICAgICAgICAgVGVybXMgb2YgU2VydmljZVxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9mb290ZXI+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJJbWFnZSIsIkxpbmsiLCJ1c2VBdXRoIiwidXNlUm91dGVyIiwiUmVhY3QiLCJCdXR0b24iLCJIb21lIiwiaXNBdXRoZW50aWNhdGVkIiwicm91dGVyIiwiaXNNb2JpbGVNZW51T3BlbiIsInNldElzTW9iaWxlTWVudU9wZW4iLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInB1c2giLCJkaXYiLCJjbGFzc05hbWUiLCJuYXYiLCJocmVmIiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiLCJwcmlvcml0eSIsImJ1dHRvbiIsInR5cGUiLCJvbkNsaWNrIiwiYXJpYS1sYWJlbCIsImFyaWEtZXhwYW5kZWQiLCJzdmciLCJmaWxsIiwidmlld0JveCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJzZWN0aW9uIiwicm9sZSIsImgxIiwic3BhbiIsInAiLCJmb3JtIiwibGFiZWwiLCJodG1sRm9yIiwiaW5wdXQiLCJpZCIsIm5hbWUiLCJwbGFjZWhvbGRlciIsImFyaWEtZGVzY3JpYmVkYnkiLCJzZWxlY3QiLCJvcHRpb24iLCJ2YWx1ZSIsImgyIiwiaDMiLCJ2YXJpYW50Iiwic2l6ZSIsImZvb3RlciIsImEiLCJ1bCIsImxpIiwiYnIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});